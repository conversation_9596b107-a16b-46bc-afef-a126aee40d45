import React from "react";

const Section3 = ({
  heading1,
  paragraph1,
  list1,
  list2,
  list3,
  heading2,
  heading3,
  paragraph2,
  paragraph3,
  heading,
    paragraph,
    heading4,
    paragraph4,
    list4,
    heading5,
    paragraph5,
    list5,
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-20">
      <div className="my-5 text-center">
        <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
          {heading}
        </h2>
        <p className="text-base md:text-lg">{paragraph}</p>
      </div>
      <div className="bg-[#F245A126] p-8 flex flex-col md:flex-row md:justify-between gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2 space-y-4">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading1}
            </h2>
            
            <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph1 }}
      />
            <div>
              <ul className="list-disc list-inside text-base md:text-lg  ">
                {list1.map((item, idx) => (
                  <li
      key={idx}
      dangerouslySetInnerHTML={{ __html: item }}
    />
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div className="md:w-[40%] bg-[#F245A126] p-4 rounded-md">
          <div className="p-2 space-y-4">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading2}
            </h2>
            
            <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph2 }}
      />
            <div>
              <ul className="list-disc list-inside text-base md:text-lg ">
                {list2.map((item, idx) => (
                 <li
      key={idx}
      dangerouslySetInnerHTML={{ __html: item }}
    />
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-[#794CEC26] p-8 flex flex-col md:flex-row md:justify-between mt-10 gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2 space-y-4">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading3}
            </h2>
              <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph3 }}
      />
            <div>
              <ul className="list-disc list-inside text-base md:text-lg ">
                {list3.map((item, idx) => (
                  <li
      key={idx}
      dangerouslySetInnerHTML={{ __html: item }}
    />
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div className="md:w-[40%] bg-[#F245A126] p-4 rounded-md">
          <div className="p-2 space-y-4">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading4}
            </h2>
               <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph4 }}
      />
            <div>
              <ul className="list-disc list-inside text-base md:text-lg ">
                {list4.map((item, idx) => (
                 <li
      key={idx}
      dangerouslySetInnerHTML={{ __html: item }}
    />
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-[#F245A126] p-8 flex flex-col md:flex-row md:justify-center gap-8">
        <div className="md:w-[40%] bg-[#794CEC26] p-4 rounded-md">
          <div className="p-2 space-y-4">
            <h2 className="text-xl md:text-3xl md:leading-[40px] font-semibold">
              {heading5}
            </h2>
            <p
        className="text-base md:text-lg text-justify "
        dangerouslySetInnerHTML={{ __html: paragraph5 }}
      />
            <div>
              <ul className="list-disc list-inside text-base md:text-lg  ">
                {list5.map((item, idx) => (
                  <li
      key={idx}
      dangerouslySetInnerHTML={{ __html: item }}
    />
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section3;
