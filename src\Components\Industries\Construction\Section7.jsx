import React from "react";

const Section7 = ({
  heading,
  spanheading,
  Data = [], // Default to empty array to avoid crashes
  headingLeft,
  paragraph,
  paragraph2,
}) => {
  return (
    <div className="bg-[#1F62EA26] py-5">
      <div className="w-[90%] mx-auto mb-10 md:mb-24">
        
        {/* Heading & Paragraph */}
        <div className="my-4">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            {heading}{" "}
            {spanheading && <span className="text-[#F245A1]">{spanheading}</span>}{" "}
            {headingLeft}
          </h2>

          {paragraph && (
            <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center mt-2">
              {paragraph}
            </p>
          )}
        </div>

        {/* Cards Section */}
        <div className="my-5">
          {Array.isArray(Data) && Data.length > 0 ? (
            Data.map((card, index) => (
              <div
                key={index}
                className="flex flex-col md:flex-row md:justify-between p-4 min-w-lg mx-auto border-b-2 border-[#7716BC]"
              >
                {/* Card Title */}
                <div className="flex items-center gap-3 md:w-[40%]">
                  <div className="bg-[#F245A1] w-8 h-8 rounded-full text-base md:text-lg text-white flex items-center justify-center">
                    {index + 1}
                  </div>
                  <h2 className="text-base md:text-lg my-2 font-semibold">
                    {card.title}
                  </h2>
                </div>

                {/* Card List */}
                <div className="md:w-[50%]">
                  {Array.isArray(card.list) && card.list.length > 0 && (
                    <ul className="list-disc list-inside text-justify text-sm text-gray-700 mt-3">
                      {card.list.map((item, idx) => (
                        <li key={idx} className="mb-2 text-base">
                          <span className="font-semibold text-[#7716BC]">
                            {item.heading}
                          </span>{" "}
                          {item.feature}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500">No data available.</p>
          )}
        </div>

        {/* Paragraph 2 */}
        {paragraph2 && (
          <div className="bg-[#350668] md:w-[70%] p-5 flex justify-center mx-auto mt-10 rounded-lg">
            <p className="text-white text-center text-base md:text-lg">
              {paragraph2}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Section7;
