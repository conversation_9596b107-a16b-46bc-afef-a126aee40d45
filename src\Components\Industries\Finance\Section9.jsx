import React from "react";
import PinkTopCard from "@/Components/PWA_development/PinkTopCard";
import PinkTopWithBullets from "./PinkTopWithBullets";

const Section9 = ({
  heading,
  PinkTopCardData,
  Cardheight,
  paragraph,
  spanHeading,
  gridClass
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 ">
      <div className="mb-7">
        <h2 className="text-xl md:text-3xl my-10 md:leading-[40px] md:w-[50%] mb-4 mx-auto font-semibold text-center">
          {heading} <span className="text-[#7716BC]">{spanHeading}</span>
        </h2>
        <p className="text-base md:text-lg text-center">{paragraph}</p>
      </div>
      <div className={`max-w-fit mx-auto grid ${gridClass ? gridClass : "md:grid-cols-3"}   gap-8 mt-6 md:mt-[0px]`}>
        {PinkTopCardData.map((card, index) => (
          <PinkTopWithBullets
            key={index}
            title={card.title}
            bulletPoints={card.bulletPoints}
            PinkTopCardheight={Cardheight}
          />
        ))}
      </div>
    </div>
  );
};

export default Section9;
