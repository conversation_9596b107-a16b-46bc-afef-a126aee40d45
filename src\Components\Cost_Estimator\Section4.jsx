"use client";
import React, { useState, useEffect } from "react";
import Button from "../Buttons/Button";
import Image from "next/image";

const Section4 = ({ heading, paragrapgh, paragraph2, buttonBg }) => {
  const [showMultiStepForm, setShowMultiStepForm] = useState(false);
  
    const scrollToExpertForm = () => {
      // Find the form section and scroll to it
      const formSection = document.getElementById("expert-form-section");
      if (formSection) {
        formSection.scrollIntoView({ behavior: "smooth" });
      }
    };
  const toggleMultiStepForm = () => {
    setShowMultiStepForm((prev) => !prev);
  };
    useEffect(() => {
        if (showMultiStepForm) {
          // Wait for the form to be in the DOM, then scroll
          const multiStepEl = document.getElementById("multi-step-form-section");
          if (multiStepEl) {
            multiStepEl.scrollIntoView({ behavior: "smooth" });
          }
        }
      }, [showMultiStepForm]); 
  return (
    <>
      <section className="bg-[#7716BC] py-8">
        <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center w-[90%] md:w-[85vw] mx-auto">
          <div className="w-full md:w-[60%] mt-6 md:mt-0">
            <h2 className="capitalize text-white text-xl md:text-2xl leading-9 text-center md:text-left font-extrabold mb-3">
              {heading}
            </h2>
            <p className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal mb-3">
              {paragrapgh}
            </p>
            <p className="w-full md:w-2/3 text-base md:text-lg text-justify text-white font-normal mb-6">
              {paragraph2}
            </p>

            {/* Changed from space-x to flex-col on mobile and row with space on larger screens */}
            <div className="flex flex-col md:flex-row md:space-x-6 space-y-4 md:space-y-0 w-full md:w-fit mx-auto md:mx-0">
              <Button bgColor="bg-[#F245A1]" paddingX="px-4" paddingY="py-2" onClick={scrollToExpertForm}>
                Estimate with our expert
              </Button>
              <Button bgColor="bg-[#F245A1]" paddingX="px-4" paddingY="py-2" onClick={toggleMultiStepForm}>
                Use our cost Estimator
              </Button>
            </div>
          </div>

          <div className="relative w-full md:w-auto flex justify-center md:block">
            <Image
              src="/Images/estimate.png"
              alt="estimate"
              className="object-contain"
              width={350}
              height={350}
              priority
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Section4;
