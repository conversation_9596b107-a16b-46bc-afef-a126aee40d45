import React from "react";
import PinkTopCard from "@/Components/PWA_development/PinkTopCard";

const Section6 = ({ heading, PinkTopCardData, Cardheight }) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 ">
      <h3 className="text-xl md:text-3xl my-10 md:leading-[40px] md:w-[50%] mx-auto font-semibold text-center">
        {heading}
      </h3>
      <div className="grid md:grid-cols-2 gap-8 mt-6 md:mt-0">
        {PinkTopCardData.map((card, index) => (
          <div key={index} className="flex justify-center items-center">
            <PinkTopCard
              title={card.title}
              description={card.description}
              PinkTopCardheight={Cardheight}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Section6;
