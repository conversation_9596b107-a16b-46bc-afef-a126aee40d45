import React from "react";
import Image from "next/image";

const Section6 = ({
  heading,
  spanheading,
  cardData,
  image,
  headingLeft,
  paragraph,
}) => {
  return (
    <div className="bg-[#1F62EA26] py-5">
      <div className="w-[90%] mx-auto mb-10 md:mb-24">
        <div className="my-4">
          <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
            {heading} <span className="text-[#F245A1]">{spanheading}</span>{" "}
            {headingLeft}
          </h2>
          <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center">
            {paragraph}{" "}
          </p>
        </div>

        <div className="flex flex-col md:flex-row justify-between gap-8 mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 w-full md:w-[50%] gap-8 ">
            {cardData.map((card, index) => (
              <div
                key={index}
                className="border border-[#F245A1] rounded-lg bg-white p-4 min-w-lg mx-auto"
              >
                <h3 className="text-base  my-2 font-semibold">{card.title}</h3>
                <p className="text-sm mb-2 text-justify">{card.description}</p>
              </div>
            ))}
          </div>
          <div className="md:w-[50%] min-h-[250px] md:min-h-0 w-full relative">
            <Image
              src={image}
              alt="AI"
              layout="fill" // Use layout="fill" to take up the entire space of the div
              objectFit="cover" // Ensures the image covers the entire space
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section6;
